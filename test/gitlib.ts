import { getWscLastHotfixBranch, getProjectRepo, getFileContent, updateAppKokoRepo, updatePackageJson } from "../src/gitlib/tools.js";
import { getProjectInfo, createBranch, getBranch } from "../src/gitlib/api.js";

// export const getWscLastHotfixBranchTest = () => {
//   return getWscLastHotfixBranch();
// };

// export const getProjectRepoTest = () => {
//   return getProjectRepo("wsc-pc-trade");
// };

// export const getProjectInfoTest = () => {
//   return getProjectInfo({ appName: "wsc-tee-h5" });
// };

// export const getRepositoryFileContentTest = () => {
//   return getFileContent({
//     appName: "wsc-tee-h5",
//     filePath: "koko.repo.json",
//     branch: "master",
//   });
// };

// export const getBranchTest = () => {
//   return getBranch({
//     appName: "wsc-tee-h5",
//     branchName: "hotfix/test-0911-1109",
//   }).then((res) => {
//     return res.name;
//   });
// };


/**
 * 生成测试分支并更新koko.repo.json文件内容
 */
// export const updateAppKokoRepoTest = () => {
//   const branchName = "hotfix/test-" + Date.now();
//   return createBranch({
//     appName: "wsc-tee-h5",
//     branchName,
//     targetBranchName: "master",
//   }).then(() => {
//     return updateAppKokoRepo({
//       appName: "wsc-tee-h5",
//       branchName,
//       subApps: ["ext-tee-wsc-ump", "ext-tee-wsc-trade"],
//     });
//   });
// };


// export const updatePackageJsonTest = () => {
//   return updatePackageJson({
//     appName: "wsc-tee-h5",
//     branchName: 'hotfix/test-1757561475246',
//     packageInfo: {
//       name: "@youzan/wsc-tee-trade-common",
//       version: "2.5.11-beta.20250807103043.0",
//     },
//   });
// };