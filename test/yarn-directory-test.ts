/**
 * 测试 yarn 在正确目录中执行的示例
 */

import { createOrSwitchBranch } from "../src/packages/npm-publish/gitbash.js";
import { Logger } from "../src/utils/logger.js";

// 测试不同应用的 yarn 执行目录

// 测试1: wsc-tee-h5 (clientDir 为空字符串，yarn 应该在根目录执行)
async function testWscTeeH5() {
  Logger.log("=== 测试 wsc-tee-h5 (根目录) ===");
  
  try {
    const result = await createOrSwitchBranch({
      appName: "wsc-tee-h5",
      branchName: "test/yarn-directory-test",
      updatePackages: false, // 只测试基本的 yarn 安装
      commitAndPush: false
    });
    
    Logger.log("wsc-tee-h5 测试结果:", result.operations);
  } catch (error: any) {
    Logger.log("wsc-tee-h5 测试失败:", error.message);
  }
}

// 测试2: wsc-pc-trade (clientDir 为 'client'，yarn 应该在 client 目录执行)
async function testWscPcTrade() {
  Logger.log("=== 测试 wsc-pc-trade (client 目录) ===");
  
  try {
    const result = await createOrSwitchBranch({
      appName: "wsc-pc-trade",
      branchName: "test/yarn-directory-test",
      updatePackages: false, // 只测试基本的 yarn 安装
      commitAndPush: false
    });
    
    Logger.log("wsc-pc-trade 测试结果:", result.operations);
  } catch (error: any) {
    Logger.log("wsc-pc-trade 测试失败:", error.message);
  }
}

// 测试3: 带包更新的完整流程
async function testWithPackageUpdate() {
  Logger.log("=== 测试带包更新的完整流程 ===");
  
  try {
    const result = await createOrSwitchBranch({
      appName: "wsc-tee-h5",
      branchName: "test/package-update-test",
      updatePackages: true,
      packageName: "@youzan/wsc-tee-trade-common",
      packageVersion: "2.5.11-beta.test",
      commitAndPush: false // 避免实际推送
    });
    
    Logger.log("包更新测试结果:", result.operations);
    
    // 检查是否执行了两次 yarn
    const yarnOperations = result.operations.filter((op: string) => 
      op.includes("yarn install")
    );
    
    Logger.log(`执行了 ${yarnOperations.length} 次 yarn install:`);
    yarnOperations.forEach((op: string, index: number) => {
      Logger.log(`  ${index + 1}. ${op}`);
    });
    
  } catch (error: any) {
    Logger.log("包更新测试失败:", error.message);
  }
}

// 运行测试（注释掉以避免实际执行）
// testWscTeeH5();
// testWscPcTrade();
// testWithPackageUpdate();

export {
  testWscTeeH5,
  testWscPcTrade,
  testWithPackageUpdate
};

/**
 * 预期的执行目录：
 * 
 * 1. wsc-tee-h5:
 *    - clientDir = '' (空字符串)
 *    - workingPath = gitDir (根目录)
 *    - yarn 在根目录执行
 * 
 * 2. wsc-pc-trade:
 *    - clientDir = 'client'
 *    - workingPath = gitDir/client
 *    - yarn 在 client 目录执行
 * 
 * 3. 包更新流程:
 *    - 第一次 yarn: 切换分支后在 workingPath 执行
 *    - API 更新 package.json
 *    - 第二次 yarn: 在 workingPath 执行以更新 yarn.lock
 * 
 * 修复内容：
 * - 确保 yarn 总是在正确的 workingPath 中执行
 * - 添加详细的日志显示执行目录
 * - 处理空字符串 clientDir 的情况
 */
