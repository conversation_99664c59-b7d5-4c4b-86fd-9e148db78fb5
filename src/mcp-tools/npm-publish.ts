
/**
 * npm包自动更新应用并提交
 */
import { z } from "zod";
import { Logger } from "~/utils/logger.js";

import npmpublish from "../packages/npm-publish/main.js";

export default function registerNpmpublishTool(server: any) {
  server.tool(
    "npm_publish_upgrade_app",
    "when npm package published, you want update the app npm version, use it",
    {
      npmName: z
        .string()
        .describe(
          "The npmName of the published package, often found in a provided string like /包名和版本：<npmName>@<npmVersion>/"
        ),
      npmVersion: z
        .string()
        .describe(
          "The npmVersion of the published package, often found in a provided string like /包名和版本：<npmName>@<npmVersion>/"
        ),
      branch: z
        .string()
        .describe(
          "The branch of the published branch, often found in a provided string like /发布分支：<branch>/"
        ),
    },
    async ({ npmName, npmVersion, branch }: any) => {
      Logger.log(
        `Updating app npm version to ${npmVersion} for ${npmName} on ${branch}`
      );
      const result = await npmpublish({ npmName, npmVersion, branch });
      return {
        content: [
          {
            type: "text",
            text: "Successfully updated app npm version. result: " + result,
          },
        ],
      };
    }
  );
}
