import { execSync } from "child_process";
import fs from "fs";
import path from "path";
import { clientDirMaps } from "./constants.js";
import { __dirname } from "~/utils/patch.js";
import { getProjectRepo } from "~/gitlib/tools.js";
import { Logger } from "~/utils/logger.js";


// Helper function to get current branch name
function getCurrentBranch(): string {
  try {
    return execSync("git branch --show-current", { encoding: "utf8" }).trim();
  } catch (error) {
    return "unknown";
  }
}

// Helper function to check if working directory is clean
function isWorkingDirectoryClean(): boolean {
  try {
    const status = execSync("git status --porcelain", { encoding: "utf8" });
    return !status.trim();
  } catch (error) {
    return false;
  }
}

// Helper function to get or clone project directory
async function getOrCloneProjectDir(
  appName: string,
  baseGithubDir = path.join(__dirname, "../github")
): Promise<string> {
  // 构建项目目录路径
  const projectDir = path.join(baseGithubDir, appName);

  // 检查项目目录是否存在
  if (fs.existsSync(projectDir)) {
    Logger.log(`✓ Project directory already exists: ${projectDir}`);
    return projectDir;
  }

  // 从 gitlab API 获取 git 地址
  let gitUrl;
  try {
    gitUrl = await getProjectRepo(appName);
    if (!gitUrl) {
      throw new Error(`Failed to get repository URL for app '${appName}'`);
    }
  } catch (error: any) {
    throw new Error(`Failed to get git repository URL for app '${appName}': ${error.message}`);
  }

  Logger.log(`Project directory not found, cloning from: ${gitUrl}`);

  // 确保基础目录存在
  if (!fs.existsSync(baseGithubDir)) {
    fs.mkdirSync(baseGithubDir, { recursive: true });
    Logger.log(`Created base directory: ${baseGithubDir}`);
  }

  // 克隆项目
  try {
    execSync(`git clone ${gitUrl} ${projectDir}`, { stdio: "inherit" });
    Logger.log(`✅ Successfully cloned project to: ${projectDir}`);

    return projectDir;
  } catch (error: any) {
    throw new Error(`Failed to clone project: ${error.message}`);
  }
}

/**
 * 创建或切换到指定分支，并可选择更新npm包
 * @param {Object} options - 配置选项
 * @param {string} options.appName - 应用名称（将根据此名称从getProjectRepo获取git地址并管理项目目录）
 * @param {string} [options.baseGithubDir] - github项目基础目录（默认为当前项目的github文件夹）
 * @param {string} options.branchName - 目标分支名称
 * @param {string} [options.baseBranch='master'] - 基础分支（当目标分支不存在时从此分支创建）
 * @param {boolean} [options.cleanWorkingDir=true] - 是否清理工作目录
 * @param {boolean} [options.updatePackages=false] - 是否更新npm包
 * @param {string} [options.packageName] - 要更新的npm包名称
 * @param {string} [options.packageVersion] - 要更新的npm包版本

 * @param {string} [options.npmRegistry='http://registry.npm.qima-inc.com/'] - npm源地址
 * @param {boolean} [options.commitAndPush=true] - 是否提交并推送更改
 * @param {string} [options.commitMessage] - 自定义提交信息
 * @returns {Promise<Object>} 返回操作结果
 */
async function createOrSwitchBranch(options: any): Promise<any> {
  const {
    appName,
    baseGithubDir = path.join(__dirname, "../github"),
    branchName,
    baseBranch = "master",
    cleanWorkingDir = true,
    updatePackages = false,
    packageName,
    packageVersion,

    npmRegistry = "http://registry.npm.qima-inc.com/",
    commitAndPush = true,
    commitMessage,
  } = options;

  // 验证必需参数
  if (!appName || !branchName) {
    throw new Error("appName and branchName are required");
  }

  // 根据应用名称获取对应的客户端目录
  const clientDir = clientDirMaps[appName];
  if (clientDir === undefined) {
    throw new Error(
      `Client directory mapping not found for app '${appName}'. Available apps: ${Object.keys(clientDirMaps).join(", ")}`
    );
  }

  // 获取或克隆项目目录
  const gitDir = await getOrCloneProjectDir(appName, baseGithubDir);

  const result: any = {
    success: false,
    currentBranch: null,
    targetBranch: branchName,
    operations: [],
  };

  try {
    // Change to the git directory
    process.chdir(gitDir);
    Logger.log(`Changed directory to: ${gitDir}`);
    result.operations.push(`Changed to directory: ${gitDir}`);

    // Check if we're in a git repository
    execSync("git rev-parse --is-inside-work-tree", { stdio: "ignore" });

    // Show current branch status
    const currentBranch = getCurrentBranch();
    result.currentBranch = currentBranch;
    Logger.log(`Current branch: ${currentBranch}`);
    Logger.log(`Target branch: ${branchName}`);

    // Check if we need to switch branches
    if (currentBranch === branchName) {
      Logger.log(`✓ Already on branch '${branchName}'`);
      if (isWorkingDirectoryClean()) {
        // Check if remote branch exists before pulling
        try {
          execSync(`git rev-parse --verify origin/${branchName}`, {
            stdio: "ignore",
          });
          Logger.log("Working directory is clean, pulling latest changes...");
          execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
          result.operations.push(
            `Pulled latest changes from origin/${branchName}`
          );
        } catch (e) {
          Logger.log(
            `Remote branch origin/${branchName} doesn't exist, skipping pull`
          );
          result.operations.push("Skipped pull - remote branch does not exist");
        }
      } else if (cleanWorkingDir) {
        Logger.log("Working directory has changes, cleaning up first...");
        // Clean working directory first, then pull
        const status = execSync("git status --porcelain", { encoding: "utf8" });
        if (status.trim()) {
          Logger.log("Found uncommitted changes:");
          Logger.log(status);
          Logger.log("Cleaning up uncommitted changes...");

          // Reset any staged and unstaged changes
          execSync("git reset --hard HEAD", { stdio: "inherit" });
          Logger.log("✓ Reset all changes to last commit");
          result.operations.push("Reset uncommitted changes");

          // Remove untracked files and directories
          execSync("git clean -fd", { stdio: "inherit" });
          Logger.log("✓ Removed untracked files and directories");
          result.operations.push("Removed untracked files");
        }

        // Check if remote branch exists before pulling
        try {
          execSync(`git rev-parse --verify origin/${branchName}`, {
            stdio: "ignore",
          });
          Logger.log("Pulling latest changes...");
          execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
          result.operations.push(
            `Pulled latest changes from origin/${branchName}`
          );
        } catch (e) {
          Logger.log(
            `Remote branch origin/${branchName} doesn't exist, skipping pull`
          );
          result.operations.push("Skipped pull - remote branch does not exist");
        }
      } else {
        Logger.log(
          "Working directory has changes, skipping pull (cleanWorkingDir=false)"
        );
        result.operations.push("Skipped pull due to uncommitted changes");
      }
    } else {
      // Check for uncommitted changes and clean them up
      if (cleanWorkingDir) {
        Logger.log("Checking for uncommitted changes...");
        try {
          const status = execSync("git status --porcelain", {
            encoding: "utf8",
          });
          if (status.trim()) {
            Logger.log("Found uncommitted changes:");
            Logger.log(status);
            Logger.log("Cleaning up uncommitted changes...");

            // Show what will be reset
            try {
              const diff = execSync("git diff --name-only", {
                encoding: "utf8",
              });
              if (diff.trim()) {
                Logger.log(
                  "Modified files that will be reset:",
                  diff.trim().split("\n")
                );
              }
            } catch (e) {
              // Ignore diff errors
            }

            // Reset any staged and unstaged changes
            execSync("git reset --hard HEAD", { stdio: "inherit" });
            Logger.log("✓ Reset all changes to last commit");
            result.operations.push("Reset uncommitted changes");

            // Remove untracked files and directories
            try {
              const untracked = execSync(
                "git ls-files --others --exclude-standard",
                { encoding: "utf8" }
              );
              if (untracked.trim()) {
                Logger.log(
                  "Untracked files that will be removed:",
                  untracked.trim().split("\n")
                );
              }
            } catch (e) {
              // Ignore if no untracked files
            }

            execSync("git clean -fd", { stdio: "inherit" });
            Logger.log("✓ Removed untracked files and directories");
            result.operations.push("Removed untracked files");

            Logger.log("✓ Working directory is now clean");
          } else {
            Logger.log("✓ Working directory is already clean");
          }
        } catch (error) {
          Logger.log("Warning: Could not check git status, continuing...");
        }
      }

      // Fetch latest changes from remote
      Logger.log("Fetching latest changes...");
      execSync("git fetch", { stdio: "inherit" });
      result.operations.push("Fetched latest changes");

      // Check if branch exists locally or remotely
      const branchExists = () => {
        try {
          execSync(`git rev-parse --verify ${branchName}`, { stdio: "ignore" });
          return true;
        } catch (e) {
          try {
            execSync(`git rev-parse --verify origin/${branchName}`, {
              stdio: "ignore",
            });
            return true;
          } catch (e) {
            return false;
          }
        }
      };

      if (branchExists()) {
        // Branch exists, checkout to it
        Logger.log(`Branch ${branchName} exists, checking out...`);
        execSync(`git checkout ${branchName}`, { stdio: "inherit" });
        result.operations.push(`Checked out existing branch: ${branchName}`);

        // Pull latest changes for existing branch
        Logger.log("Pulling latest changes...");
        execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
        result.operations.push(
          `Pulled latest changes from origin/${branchName}`
        );
      } else {
        // Branch doesn't exist, create from base branch
        Logger.log(
          `Branch ${branchName} doesn't exist, creating from ${baseBranch}...`
        );
        execSync(`git checkout ${baseBranch}`, { stdio: "inherit" });
        execSync(`git pull origin ${baseBranch}`, { stdio: "inherit" });
        execSync(`git checkout -b ${branchName}`, { stdio: "inherit" });
        result.operations.push(
          `Created new branch: ${branchName} from ${baseBranch}`
        );
      }
    }

    // Update npm packages if requested
    if (updatePackages) {
      Logger.log("\n🔄 Starting npm package update...");

      // Validate package parameters
      if (!packageName || !packageVersion) {
        throw new Error(
          "packageName and packageVersion are required when updatePackages is true"
        );
      }

      // 确定工作目录路径
      const workingPath = clientDir ? path.join(gitDir, clientDir) : gitDir;

      // Check if working directory exists
      if (!fs.existsSync(workingPath)) {
        throw new Error(`Working directory not found: ${workingPath}`);
      }

      Logger.log(`Entering working directory: ${workingPath}`);
      process.chdir(workingPath);
      result.operations.push(`Changed to working directory: ${workingPath}`);

      // 使用全局 yarn 命令
      const yarnCommand = "yarn";
      Logger.log(`✅ Using global yarn: ${yarnCommand}`);
      result.operations.push(`Using yarn: ${yarnCommand}`);

      // Set npm registry
      Logger.log(`Setting npm registry to: ${npmRegistry}`);
      execSync(`${yarnCommand} config set registry ${npmRegistry}`, {
        stdio: "inherit",
      });
      result.operations.push(`Set npm registry to: ${npmRegistry}`);

      // Update the specific package
      const packageSpec = packageVersion
        ? `${packageName}@${packageVersion}`
        : packageName;
      Logger.log(`Updating package: ${packageSpec}`);
      execSync(`${yarnCommand} add ${packageSpec}`, { stdio: "inherit" });
      result.operations.push(`Updated package: ${packageSpec}`);

      Logger.log("✅ Package update completed successfully!");

      // Commit and push changes if requested
      if (commitAndPush) {
        Logger.log("\n📤 Committing and pushing changes...");

        // Go back to git root directory
        process.chdir(gitDir);

        // Generate commit message
        const defaultCommitMessage = `feat: update ${packageName} to ${packageVersion || "latest"}`;
        const finalCommitMessage = commitMessage || defaultCommitMessage;

        // Add package.json and yarn.lock changes
        const packageJsonPath = clientDir
          ? `${clientDir}/package.json`
          : "package.json";
        const yarnLockPath = clientDir ? `${clientDir}/yarn.lock` : "yarn.lock";
        execSync(`git add ${packageJsonPath}`, { stdio: "inherit" });
        execSync(`git add ${yarnLockPath}`, { stdio: "inherit" });
        Logger.log("Added package.json and yarn.lock to git");
        result.operations.push("Added package files to git");

        // Check if there are changes to commit
        try {
          const status = execSync("git status --porcelain", {
            encoding: "utf8",
          });
          if (status.trim()) {
            // Commit changes
            execSync(`git commit -m "${finalCommitMessage}"`, {
              stdio: "inherit",
            });
            Logger.log(`Committed changes: ${finalCommitMessage}`);
            result.operations.push(`Committed: ${finalCommitMessage}`);

            // Push to remote
            execSync(`git push -u origin ${branchName}`, { stdio: "inherit" });
            Logger.log(`✅ Successfully pushed to origin/${branchName}`);
            result.operations.push(`Pushed to origin/${branchName}`);
          } else {
            Logger.log("No changes to commit (package files unchanged)");
            result.operations.push("No changes to commit");
          }
        } catch (error) {
          Logger.log("Warning: Could not check git status for commit");
          result.operations.push("Warning: Git status check failed");
        }
      }
    }

    result.success = true;
    return result;
  } catch (error: any) {
    result.error = error.message;
    throw error;
  }
}

// 导出函数供其他模块使用
export {
  createOrSwitchBranch,
  getCurrentBranch,
  isWorkingDirectoryClean,
  getOrCloneProjectDir,
};

export default {
  createOrSwitchBranch,
  getCurrentBranch,
  isWorkingDirectoryClean,
  getOrCloneProjectDir,
};
